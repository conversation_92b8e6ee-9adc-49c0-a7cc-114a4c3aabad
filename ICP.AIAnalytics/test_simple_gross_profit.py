#!/usr/bin/env python3
"""
Simple test to verify LLM response for missing Gross Profit data.
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.validation_service import ValidationService

# Simple XML with missing Gross Profit
XML_MISSING_GROSS_PROFIT = """
<Report>
    <FinancialSection>
        <TotalIncome>100000.00</TotalIncome>
        <NetProfit>20000.00</NetProfit>
        <Revenue>150000.00</Revenue>
    </FinancialSection>
</Report>
"""

# Simple XML with both values present
XML_WITH_BOTH = """
<Report>
    <FinancialSection>
        <GrossProfit>80000.00</GrossProfit>
        <TotalIncome>100000.00</TotalIncome>
    </FinancialSection>
</Report>
"""

async def test_llm_response():
    """Test LLM response directly."""
    print("Testing LLM response for Gross Profit validation...")
    print("=" * 60)
    
    validation_service = ValidationService()
    question = "In the financial section Gross Profit should be less than Total Income"
    
    # Test 1: Missing Gross Profit
    print("\nTest 1: Missing Gross Profit")
    print("-" * 30)
    print("XML Content:")
    print(XML_MISSING_GROSS_PROFIT)
    
    try:
        response = await validation_service._generate_llm_response(
            question, XML_MISSING_GROSS_PROFIT, 1
        )
        print(f"LLM Response: {response}")
        
        # Parse the JSON response
        import json
        parsed = json.loads(response)
        print(f"Summary: {parsed.get('summary', 'N/A')}")
        print(f"Status: {parsed.get('status', 'N/A')}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Both values present
    print("\n" + "=" * 60)
    print("\nTest 2: Both values present")
    print("-" * 30)
    print("XML Content:")
    print(XML_WITH_BOTH)
    
    try:
        response = await validation_service._generate_llm_response(
            question, XML_WITH_BOTH, 1
        )
        print(f"LLM Response: {response}")
        
        # Parse the JSON response
        parsed = json.loads(response)
        print(f"Summary: {parsed.get('summary', 'N/A')}")
        print(f"Status: {parsed.get('status', 'N/A')}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_llm_response())
