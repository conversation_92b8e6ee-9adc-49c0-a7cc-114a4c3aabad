#!/usr/bin/env python3
"""
Test script to verify the "Gross Profit should be less than Total Income" validation rule
handles missing data correctly.
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.validation_service import ValidationService
from app.models.schemas import Question

# Test data: XML reports with different scenarios
TEST_XML_WITH_BOTH_VALUES = {
    "xml_data": {
        "Report": {
            "FinancialSection": {
                "GrossProfit": "80000.00",
                "TotalIncome": "100000.00"
            }
        }
    }
}

TEST_XML_MISSING_GROSS_PROFIT = {
    "xml_data": {
        "Report": {
            "FinancialSection": {
                "TotalIncome": "100000.00",
                "NetProfit": "20000.00"
            }
        }
    }
}

TEST_XML_MISSING_TOTAL_INCOME = {
    "xml_data": {
        "Report": {
            "FinancialSection": {
                "GrossProfit": "80000.00",
                "NetProfit": "20000.00"
            }
        }
    }
}

TEST_XML_MISSING_BOTH = {
    "xml_data": {
        "Report": {
            "FinancialSection": {
                "NetProfit": "20000.00",
                "Revenue": "150000.00"
            }
        }
    }
}

# Test question
GROSS_PROFIT_QUESTION = Question(
    id="test-gross-profit-rule",
    question="In the financial section Gross Profit should be less than Total Income",
    darwin_reference_sections="Report/FinancialSection",
    category="financial",
    client_code=None
)

async def test_gross_profit_validation():
    """Test the gross profit validation rule with different scenarios."""
    print("Testing Gross Profit vs Total Income validation rule...")
    print("=" * 60)
    
    validation_service = ValidationService()
    
    test_cases = [
        {
            "name": "Both values present (compliant)",
            "xml_data": TEST_XML_WITH_BOTH_VALUES,
            "expected_status": "approved"
        },
        {
            "name": "Missing Gross Profit",
            "xml_data": TEST_XML_MISSING_GROSS_PROFIT,
            "expected_status": "manual_intervention_needed"
        },
        {
            "name": "Missing Total Income", 
            "xml_data": TEST_XML_MISSING_TOTAL_INCOME,
            "expected_status": "manual_intervention_needed"
        },
        {
            "name": "Missing both values",
            "xml_data": TEST_XML_MISSING_BOTH,
            "expected_status": "manual_intervention_needed"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Show the XML content being processed
            xml_content = validation_service._dict_to_xml_string(test_case["xml_data"]["xml_data"])
            print(f"XML Content: {xml_content[:200]}...")

            # Test the validation logic directly
            result = await validation_service._validate_single_question(
                GROSS_PROFIT_QUESTION,
                test_case["xml_data"],
                1,  # question_number
                False,  # enable_client_filtering
                None,   # order_client_code
                None    # focus_prompt
            )
            
            print(f"Summary: {result.summary}")
            print(f"Status: {result.status}")
            print(f"Confidence: {result.confidence_score}")
            print(f"Expected Status: {test_case['expected_status']}")
            
            # Check if the result matches expectations
            if result.status == test_case["expected_status"]:
                print("✅ PASS - Status matches expected")
            else:
                print("❌ FAIL - Status does not match expected")
                
            # For missing data cases, check if summary mentions the missing data
            if "missing" in test_case["name"].lower():
                if any(phrase in result.summary.lower() for phrase in [
                    "not present", "missing", "cannot verify", "not found"
                ]):
                    print("✅ PASS - Summary correctly indicates missing data")
                else:
                    print("❌ FAIL - Summary does not indicate missing data")
                    
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            import traceback
            traceback.print_exc()

async def test_status_determination():
    """Test the _determine_status method directly."""
    print("\n" + "=" * 60)
    print("Testing _determine_status method directly...")
    print("=" * 60)
    
    validation_service = ValidationService()
    
    test_summaries = [
        {
            "summary": "Gross Profit is not present in the financial data, cannot verify compliance",
            "question": "In the financial section Gross Profit should be less than Total Income",
            "expected": "manual_intervention_needed"
        },
        {
            "summary": "Total Income is not present in the financial data, cannot verify compliance", 
            "question": "In the financial section Gross Profit should be less than Total Income",
            "expected": "manual_intervention_needed"
        },
        {
            "summary": "Neither Gross Profit nor Total Income are present, cannot verify",
            "question": "In the financial section Gross Profit should be less than Total Income", 
            "expected": "manual_intervention_needed"
        },
        {
            "summary": "Gross Profit '80000.00' is less than Total Income '100000.00', compliant",
            "question": "In the financial section Gross Profit should be less than Total Income",
            "expected": "approved"
        }
    ]
    
    for i, test in enumerate(test_summaries, 1):
        print(f"\nTest {i}: {test['summary'][:50]}...")
        result = validation_service._determine_status(
            test["summary"], 
            test["question"], 
            0.9  # confidence_score
        )
        print(f"Result: {result}")
        print(f"Expected: {test['expected']}")
        
        if result == test["expected"]:
            print("✅ PASS")
        else:
            print("❌ FAIL")

if __name__ == "__main__":
    print("Gross Profit Validation Rule Test")
    print("=" * 60)
    
    # Run the tests
    asyncio.run(test_gross_profit_validation())
    asyncio.run(test_status_determination())
    
    print("\n" + "=" * 60)
    print("Test completed!")
